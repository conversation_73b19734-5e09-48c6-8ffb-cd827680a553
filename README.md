# InputOutput Node.js API Server

A simple Node.js server with Express and MongoDB for saving token transaction data.

## Features

- RESTful API with POST endpoint for saving token data
- MongoDB integration using Mongoose
- Organized folder structure with separate routes and controllers
- Input validation and error handling
- Environment configuration support

## Project Structure

```
├── controllers/
│   └── tokenController.js    # Business logic for token operations
├── models/
│   └── Token.js             # Mongoose schema for token data
├── routes/
│   └── tokenRoutes.js       # Route definitions
├── .env                     # Environment variables
├── server.js               # Main server file
└── package.json            # Dependencies and scripts
```

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)

## Installation

1. Install dependencies (already done):
   ```bash
   npm install
   ```

2. Configure environment variables:
   - Update the `.env` file with your MongoDB connection string
   - For local MongoDB: `mongodb://localhost:27017/inputoutput`
   - For MongoDB Atlas: `mongodb+srv://username:<EMAIL>/inputoutput`

## Usage

### Start the server

```bash
npm start
```

The server will start on port 3000 (or the port specified in the .env file).

### API Endpoints

#### Health Check
- **GET** `/health`
- Returns server status

#### Root Endpoint
- **GET** `/`
- Returns welcome message and available endpoints

#### Save Token Data
- **POST** `/api/tokens`
- Saves token transaction data to MongoDB

**Request Body:**
```json
{
  "inputTokenAddress": "0x1234567890abcdef",
  "outputTokenAddress": "0xabcdef1234567890",
  "inputAmount": 100.5,
  "outputAmount": 95.2,
  "userAccount": "0xuser1234567890"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Token data saved successfully",
  "data": {
    "_id": "...",
    "inputTokenAddress": "0x1234567890abcdef",
    "outputTokenAddress": "0xabcdef1234567890",
    "inputAmount": 100.5,
    "outputAmount": 95.2,
    "userAccount": "0xuser1234567890",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "message": "All fields are required: inputTokenAddress, outputTokenAddress, inputAmount, outputAmount, userAccount"
}
```

## Testing the API

You can test the API using curl, Postman, or any HTTP client:

```bash
curl -X POST http://localhost:3000/api/tokens \
  -H "Content-Type: application/json" \
  -d '{
    "inputTokenAddress": "0x1234567890abcdef",
    "outputTokenAddress": "0xabcdef1234567890",
    "inputAmount": 100.5,
    "outputAmount": 95.2,
    "userAccount": "0xuser1234567890"
  }'
```

## Data Validation

The API validates:
- All 5 fields are required
- Input and output amounts must be positive numbers
- String fields are trimmed of whitespace

## Environment Variables

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment mode (development/production)
- `MONGODB_URI`: MongoDB connection string
