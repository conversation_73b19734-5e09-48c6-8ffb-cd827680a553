const mongoose = require("mongoose");

const tokenSchema = new mongoose.Schema(
  {
    inputTokenAddress: {
      type: String,
    },
    outputTokenAddress: {
      type: String,
    },
    inputAmount: {
      type: Number,
      min: 0,
    },
    outputAmount: {
      type: Number,
      min: 0,
    },
    userAccount: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("Token", tokenSchema);
