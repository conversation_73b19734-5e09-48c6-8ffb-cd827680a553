const Token = require('../models/Token');

// Create a new token record
const createToken = async (req, res) => {
  try {
    const { inputTokenAddress, outputTokenAddress, inputAmount, outputAmount, userAccount } = req.body;

    // Validate required fields
    if (!inputTokenAddress || !outputTokenAddress || !inputAmount || !outputAmount || !userAccount) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required: inputTokenAddress, outputTokenAddress, inputAmount, outputAmount, userAccount'
      });
    }

    // Validate that amounts are positive numbers
    if (inputAmount <= 0 || outputAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Input amount and output amount must be positive numbers'
      });
    }

    // Create new token record
    const newToken = new Token({
      inputTokenAddress,
      outputTokenAddress,
      inputAmount,
      outputAmount,
      userAccount
    });

    // Save to database
    const savedToken = await newToken.save();

    res.status(201).json({
      success: true,
      message: 'Token data saved successfully',
      data: savedToken
    });

  } catch (error) {
    console.error('Error saving token data:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: Object.values(error.errors).map(err => err.message)
      });
    }

    // Handle other errors
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
  }
};

module.exports = {
  createToken
};
